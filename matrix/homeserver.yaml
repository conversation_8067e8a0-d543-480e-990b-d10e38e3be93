server_name: "${<PERSON><PERSON><PERSON><PERSON>_NAME}"
pid_file: /data/homeserver.pid
listeners:
  - port: 8008
    tls: false
    type: http
    x_forwarded: true
    resources:
      - names: [client, federation]
        compress: false

database:
  name: psycopg2
  args:
    user: ${POSTGRES_USER}
    password: ${POSTGRES_PASSWORD}
    database: ${POSTGRES_DB}
    host: postgres
    port: 5432
    cp_min: 5
    cp_max: 10

log_config: "/data/${DOMAIN_NAME}.log.config"
media_store_path: /data/media_store
registration_shared_secret: "${REGISTRATION_SHARED_SECRET}"
report_stats: false
macaroon_secret_key: "${REGISTRATION_SHARED_SECRET}"
form_secret: "${REGISTRATION_SHARED_SECRET}"
signing_key_path: "/data/${DOMAIN_NAME}.signing.key"

trusted_key_servers:
  - server_name: "matrix.org"

# Enable registration for setup
enable_registration: true
enable_registration_without_verification: true

# Allow guest access
allow_guest_access: false

# URL previews
url_preview_enabled: true
url_preview_ip_range_blacklist:
  - '*********/8'
  - '10.0.0.0/8'
  - '**********/12'
  - '***********/16'
  - '**********/10'
  - '***********/16'
  - '::1/128'
  - 'fe80::/64'
  - 'fc00::/7'

# Federation settings
federation_domain_whitelist: []

# File upload settings
max_upload_size: 50M
max_image_pixels: 32M

# Enable presence
use_presence: true

# Retention policy
retention:
  enabled: false

# privacy https://matrix.org/blog/2019/11/09/avoiding-unwelcome-visitors-on-private-matrix-servers/
allow_public_rooms_without_auth: false
allow_public_rooms_over_federation: false
enable_registration: false
