#!/bin/bash

# Setup script to create all required directories for kapsule
# Run this before docker-compose up on a fresh clone

set -e

echo "=== Setting up directory structure for kapsule ==="

# Create main data directory
mkdir -p ../data

# Create subdirectories for each service
echo "Creating service data directories..."
mkdir -p ../data/postgres
mkdir -p ../data/synapse
mkdir -p ../data/mautrix-whatsapp
mkdir -p ../data/mautrix-imessage
mkdir -p ../data/mautrix-meta

# Set proper permissions for postgres (it's picky about ownership)
echo "Setting permissions..."
chmod 755 ../data/postgres

# Create config directories if they don't exist
mkdir -p ../config/mautrix-whatsapp
mkdir -p ../config/mautrix-imessage
mkdir -p ../config/mautrix-meta

echo "✅ Directory structure created successfully!"
echo ""
echo "Directory tree:"
tree ../data ../config 2>/dev/null || find ../data ../config -type d 2>/dev/null || echo "Use 'ls -la ../data' and 'ls -la ../config' to verify structure"
echo ""
echo "You can now run: docker-compose up -d" 