# Kapsule Matrix Server

A complete Matrix homeserver setup with Element web client, supporting both local development and cloud deployment via Cloudflare Tunnel.

## Overview

This setup provides a fully functional Matrix homeserver with:
- **Matrix Synapse**: The reference Matrix homeserver implementation
- **Element Web**: Modern Matrix web client interface
- **PostgreSQL**: Database backend for Synapse
- **Nginx**: Reverse proxy with SSL termination
- **Cloudflare Tunnel**: Optional cloud connectivity (cloud mode only)

The system supports two deployment modes:
1. **Local Mode**: For development/testing with self-signed certificates
2. **Cloud Mode**: For production with Cloudflare Tunnel integration

## Directory Structure
```
matrix/
├── docker-compose.yml          # Main Docker Compose configuration
├── .env                        # Environment variables (create this)
├── nginx.conf                  # Nginx reverse proxy configuration
├── homeserver.yaml             # Synapse configuration template
├── element-config.json         # Element web client configuration
├── postgres-init.sh            # Database initialization script
├── entrypoint-synapse.sh       # Synapse startup script
├── entrypoint-element.sh       # Element startup script
├── start-local.sh              # Local deployment script
├── start-cloud.sh              # Cloud deployment script
├── stop.sh                     # Stop all services
├── certs/                      # SSL certificates for local mode
│   ├── lifecapsule.local.crt   # Local SSL certificate
│   └── lifecapsule.local.key   # Local SSL private key
└── ../data/                    # Persistent data (auto-created)
    ├── synapse/                # Synapse data directory
    └── postgres/               # PostgreSQL data directory
```

## Prerequisites

1. **Docker & Docker Compose**: Ensure both are installed and running
2. **Environment File**: Create a `.env` file (see configuration section)
3. **Local Mode Only**: Install certificates in macOS Keychain (see local setup)

## Configuration

### 1. Create Environment File
Create a `.env` file in the matrix directory:

```bash
# Domain Configuration
DOMAIN_NAME=lifecapsule.local

# Database Configuration
POSTGRES_USER=synapse
POSTGRES_PASSWORD=your_secure_password_here
POSTGRES_DB=synapse

# Synapse Configuration
REGISTRATION_SHARED_SECRET=your_random_secret_here

# Cloud Mode Only - Get from Cloudflare Tunnel dashboard
CLOUDFLARE_TUNNEL_TOKEN=your_tunnel_token_here
```

**Generate secure values:**
```bash
# Generate registration secret
openssl rand -hex 32

# Generate database password
openssl rand -base64 32
```

### 2. Make Scripts Executable
```bash
chmod +x *.sh
```

## Local Development Setup

### 1. Install SSL Certificates in Keychain
For local HTTPS to work without browser warnings, add the certificate to your macOS Keychain:

```bash
# Add certificate to Keychain
sudo security add-trusted-cert -d -r trustRoot -k /Library/Keychains/System.keychain certs/lifecapsule.local.crt

# Or use Keychain Access GUI:
# 1. Open Keychain Access
# 2. File > Import Items
# 3. Select certs/lifecapsule.local.crt
# 4. Double-click the certificate
# 5. Expand "Trust" section
# 6. Set "When using this certificate" to "Always Trust"
```

### 2. Add Local Domain to Hosts File
```bash
echo "127.0.0.1 lifecapsule.local" | sudo tee -a /etc/hosts
```

### 3. Start Local Services
```bash
./start-local.sh
```

### 4. Create Admin User
```bash
docker exec -it kapsule-synapse register_new_matrix_user -c /data/homeserver.yaml http://localhost:8008
```

### 5. Access Local Instance
- **Element Web**: https://lifecapsule.local
- **Matrix API**: https://lifecapsule.local/_matrix
- **Admin API**: https://lifecapsule.local/_synapse/admin

## Cloud Deployment Setup

### 1. Configure Cloudflare Tunnel
In your Cloudflare dashboard:
1. Create a new tunnel
2. Point your domain to `http://localhost:80`
3. Copy the tunnel token to your `.env` file

### 2. Update Domain Configuration
Edit `.env` to use your actual domain:
```bash
DOMAIN_NAME=matrix.yourdomain.com
CLOUDFLARE_TUNNEL_TOKEN=your_actual_tunnel_token
```

### 3. Start Cloud Services
```bash
./start-cloud.sh
```

### 4. Create Admin User
```bash
docker exec -it kapsule-synapse register_new_matrix_user -c /data/homeserver.yaml http://localhost:8008
```

### 5. Access Cloud Instance
- **Element Web**: https://matrix.yourdomain.com
- **Matrix API**: https://matrix.yourdomain.com/_matrix
- **Admin API**: https://matrix.yourdomain.com/_synapse/admin

## Service Management

### Start Services
```bash
# Local mode
./start-local.sh

# Cloud mode
./start-cloud.sh
```

### Stop Services
```bash
./stop.sh
```

### View Logs
```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f synapse
docker-compose logs -f element-web
docker-compose logs -f nginx
```

### Restart Services
```bash
docker-compose restart
```

## Troubleshooting

### Common Issues

**Certificate Errors (Local Mode)**
- Ensure certificate is installed in Keychain and trusted
- Verify `lifecapsule.local` is in `/etc/hosts`
- Clear browser cache/data for the domain

**Database Connection Issues**
- Check PostgreSQL container is running: `docker ps`
- Verify database credentials in `.env`
- Check database logs: `docker-compose logs postgres`

**Synapse Won't Start**
- Check configuration: `docker-compose logs synapse`
- Verify database is accessible
- Ensure data directory permissions are correct

**Element Web Issues**
- Check if Synapse is accessible
- Verify nginx configuration
- Check Element logs: `docker-compose logs element-web`

### Useful Commands

```bash
# Check service status
docker-compose ps

# Access Synapse container
docker exec -it kapsule-synapse /bin/bash

# Reset all data (WARNING: Destructive)
docker-compose down -v
sudo rm -rf ../data/

# View real-time logs
docker-compose logs -f --tail=100
```

## Security Notes

- **Local Mode**: Uses self-signed certificates, suitable for development only
- **Cloud Mode**: SSL handled by Cloudflare, production-ready
- Change default passwords in `.env` before deployment
- Keep registration secret secure - it allows creating new users
- Consider enabling registration only when needed
- Regular backups of `../data/` directory recommended

## Federation

- **Local Mode**: Federation not available (local domain only)
- **Cloud Mode**: Federation works automatically once domain is accessible
- Ensure your domain's `.well-known` delegation is configured if using a subdomain