events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    sendfile on;
    keepalive_timeout 65;
    client_max_body_size 50M;

    #
    # 1) Default HTTP server: catch-all for “main” (homeserver) on port 80
    #
    server {
        listen 80 default_server;
        server_name _;

        # Matrix Federation well-known
        location /.well-known/matrix/server {
            return 200 '{"m.server": "$host:443"}';
            add_header Content-Type application/json;
            add_header Access-Control-Allow-Origin *;
        }
        location /.well-known/matrix/client {
            return 200 '{"m.homeserver": {"base_url": "https://$host"}, "m.identity_server": {"base_url": "https://vector.im"}}';
            add_header Content-Type application/json;
            add_header Access-Control-Allow-Origin *;
        }

        # Landing page
        location / {
            root /usr/share/nginx/html;
            try_files /index.html =404;
        }

        # Matrix Synapse API
        location /_matrix {
            proxy_pass http://synapse:8008;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto https;
            client_max_body_size 50M;
        }

        # Synapse Admin interface
        location /_synapse/admin/ {
            # API versions
            location ~ ^/_synapse/admin/v[0-9]+/ {
                proxy_pass http://synapse:8008;
                proxy_set_header Host $host;
                proxy_set_header X-Forwarded-For $remote_addr;
                proxy_set_header X-Forwarded-Proto https;
                proxy_read_timeout 60s;
                proxy_connect_timeout 60s;
                proxy_send_timeout 60s;
                client_max_body_size 50M;
            }
            # UI
            proxy_pass http://synapse-admin-web/;
            proxy_set_header Host $host;
            proxy_set_header X-Forwarded-For $remote_addr;
            proxy_set_header X-Forwarded-Proto https;
            proxy_read_timeout 60s;
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
        }

        location /_synapse {
            proxy_pass http://synapse:8008;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto https;
        }
    }

    #
    # 2) HTTP server for Element subdomain: matches element.<anything>
    #
    server {
        listen 80;
        # regex: host starting with “element.”
        server_name ~^element\..+$;

        location / {
            proxy_pass http://element-web:80;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto https;
        }
    }

    #
    # 3) HTTP server for Hydrogen subdomain: matches hydrogen.<anything>
    #
    server {
        listen 80;
        server_name ~^hydrogen\..+$;

        location / {
            proxy_pass http://hydrogen-web:8080;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto https;
        }
    }

    #
    # 4) Default HTTPS server for “main” on port 443
    #
    server {
        listen 443 ssl http2 default_server;
        server_name _;

        # SSL Configuration
        ssl_certificate /etc/nginx/certs/lifecapsule.local.crt;
        ssl_certificate_key /etc/nginx/certs/lifecapsule.local.key;
        ssl_protocols TLSv1.2 TLSv1.3;

        location /.well-known/matrix/server {
            return 200 '{"m.server": "$host:443"}';
            add_header Content-Type application/json;
            add_header Access-Control-Allow-Origin *;
        }
        location /.well-known/matrix/client {
            return 200 '{"m.homeserver": {"base_url": "https://$host"}, "m.identity_server": {"base_url": "https://vector.im"}}';
            add_header Content-Type application/json;
            add_header Access-Control-Allow-Origin *;
        }

        location / {
            root /usr/share/nginx/html;
            try_files /index.html =404;
        }

        location /_matrix {
            proxy_pass http://synapse:8008;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            client_max_body_size 50M;
        }

        location /_synapse/admin/ {
            location ~ ^/_synapse/admin/v[0-9]+/ {
                proxy_pass http://synapse:8008;
                proxy_set_header Host $host;
                proxy_set_header X-Forwarded-For $remote_addr;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_read_timeout 60s;
                proxy_connect_timeout 60s;
                proxy_send_timeout 60s;
                client_max_body_size 50M;
            }
            proxy_pass http://synapse-admin-web/;
            proxy_set_header Host $host;
            proxy_set_header X-Forwarded-For $remote_addr;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_read_timeout 60s;
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
        }

        location /_synapse {
            proxy_pass http://synapse:8008;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

    #
    # 5) HTTPS for Element subdomain
    #
    server {
        listen 443 ssl http2;
        server_name ~^element\..+$;

        ssl_certificate /etc/nginx/certs/lifecapsule.local.crt;
        ssl_certificate_key /etc/nginx/certs/lifecapsule.local.key;
        ssl_protocols TLSv1.2 TLSv1.3;

        location / {
            proxy_pass http://element-web:80;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

    #
    # 6) HTTPS for Hydrogen subdomain
    #
    server {
        listen 443 ssl http2;
        server_name ~^hydrogen\..+$;

        ssl_certificate /etc/nginx/certs/lifecapsule.local.crt;
        ssl_certificate_key /etc/nginx/certs/lifecapsule.local.key;
        ssl_protocols TLSv1.2 TLSv1.3;

        location / {
            proxy_pass http://hydrogen-web:8080;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}