#!/bin/sh
set -e

# Install gettext for envsubst
apt-get update && apt-get install -y gettext-base && rm -rf /var/lib/apt/lists/*

# # Generate initial config if it doesn't exist
# if [ ! -f /data/homeserver.yaml ]; then
#     echo "Generating initial Synapse configuration..."
#     python -m synapse.app.homeserver \
#         --server-name="${DOMAIN_NAME}" \
#         --config-path=/data/homeserver.yaml \
#         --generate-config \
#         --report-stats=no
# fi

# Process template and update homeserver.yaml
if [ -f /templates/homeserver.yaml ]; then
    echo "Processing homeserver.yaml template..."
    envsubst < /templates/homeserver.yaml > /data/homeserver.yaml
fi

# Set ownership
chown -R 991:991 /data

# Start Synapse
exec python -m synapse.app.homeserver --config-path=/data/homeserver.yaml