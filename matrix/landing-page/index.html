<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .container {
            text-align: center;
            max-width: 800px;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        h1 {
            font-size: 4rem;
            font-weight: 300;
            margin-bottom: 1rem;
            letter-spacing: 2px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            margin-bottom: 3rem;
            opacity: 0.9;
            font-weight: 300;
        }

        .links {
            display: flex;
            gap: 2rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .link-card {
            background: rgba(255, 255, 255, 0.15);
            padding: 1.5rem 2rem;
            border-radius: 15px;
            text-decoration: none;
            color: white;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
            min-width: 150px;
        }

        .link-card:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        .link-card h3 {
            font-size: 1.3rem;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .link-card p {
            font-size: 0.9rem;
            opacity: 0.8;
            font-weight: 300;
        }

        .matrix-info {
            margin-top: 3rem;
            padding-top: 2rem;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }

        .matrix-info p {
            font-size: 1rem;
            opacity: 0.8;
            line-height: 1.6;
        }

        @media (max-width: 768px) {
            h1 {
                font-size: 2.5rem;
            }
            
            .container {
                margin: 1rem;
                padding: 1.5rem;
            }
            
            .links {
                flex-direction: column;
                align-items: center;
            }
            
            .link-card {
                width: 100%;
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Legacii Kapsule</h1>
        <p class="subtitle">Your Personal Matrix Homeserver</p>
        
        <div class="links">
            <a href="#" id="element-link" class="link-card">
                <h3>Element</h3>
                <p>Full-featured Matrix client</p>
            </a>

            <a href="#" id="hydrogen-link" class="link-card">
                <h3>Hydrogen</h3>
                <p>Lightweight Matrix client</p>
            </a>

            <a href="/_synapse/admin/" class="link-card">
                <h3>Admin</h3>
                <p>Server administration</p>
            </a>
        </div>

        <script>
            // Set subdomain links based on current domain
            const domain = window.location.hostname;
            const protocol = window.location.protocol;
            document.getElementById('element-link').href = `${protocol}//element.${domain}`;
            document.getElementById('hydrogen-link').href = `${protocol}//hydrogen.${domain}`;
        </script>
        
        <div class="matrix-info">
            <p>Welcome to your Matrix homeserver. Choose a client above to start chatting securely with friends, family, and communities across the Matrix network.</p>
        </div>
    </div>
</body>
</html>
