# version: '3.8'
name: kapsule

services:

  synapse:
    image: matrixdotorg/synapse:latest
    container_name: kapsule-synapse
    # restart: unless-stopped
    ports:
      - "8008:8008"
    volumes:
      - ../data/synapse:/data
      - ./homeserver.yaml:/templates/homeserver.yaml:ro
      - ./entrypoint-synapse.sh:/entrypoint.sh:ro
    environment:
      - SYNAPSE_SERVER_NAME=${DOMAIN_NAME}
      - SYNAPSE_REPORT_STATS=no
      - DOMAIN_NAME=${DOMAIN_NAME}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
      - REGISTRATION_SHARED_SECRET=${REGISTRATION_SHARED_SECRET}      
    entrypoint: ["/bin/sh", "/entrypoint.sh"]
    networks:
      - kapsulenet
    depends_on:
      - postgres-init

  synapse-admin-web:
    image: ghcr.io/etkecc/synapse-admin:latest # awesometechnologies/synapse-admin:latest
    container_name: kapsule-synapse-admin-web
    # restart: unless-stopped
    # ports:
    #   - "${SYNAPSE_ADMIN_WEB_PORT-8081}:8081"
    networks:
      - kapsulenet

  element-web:
    image: vectorim/element-web:latest
    container_name: kapsule-element-web
    user: root
    # restart: unless-stopped
    volumes:
      - ./element-config.json:/templates/element-config.json:ro
      - ./entrypoint-element.sh:/entrypoint.sh:ro
    environment:
      - DOMAIN_NAME=${DOMAIN_NAME}
    entrypoint: ["/bin/sh", "/entrypoint.sh"]
    networks:
      - kapsulenet

  hydrogen-web:
    image: ghcr.io/element-hq/hydrogen-web:latest
    container_name: kapsule-hydrogen-web
    # restart: unless-stopped
    environment:
      - HYDROGEN_CONFIG={"defaultHomeServer":"https://${DOMAIN_NAME}","themeManifests":[{"name":"Element","id":"element"},{"name":"Dark","id":"dark","default":true}],"features":{"calls":false}}
    networks:
      - kapsulenet

  nginx-local:
    image: nginx:alpine
    container_name: kapsule-nginx-local
    # restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./certs:/etc/nginx/certs:ro
      - ./landing-page:/usr/share/nginx/html:ro
    networks:
      - kapsulenet
    depends_on:
      - synapse
      - element-web
      - hydrogen-web
    profiles: ["local"]

  nginx: # keep this name as it's what is set in the cloudflared service
    image: nginx:alpine
    container_name: kapsule-nginx-tunnel
    # restart: unless-stopped
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./certs:/etc/nginx/certs:ro
      - ./landing-page:/usr/share/nginx/html:ro
    networks:
      - kapsulenet
    depends_on:
      - synapse
      - element-web
    profiles: ["tunnel"]

  cloudflared:
    image: cloudflare/cloudflared:latest
    container_name: kapsule-cloudflared
    # restart: unless-stopped
    command: tunnel run
    environment:
      - TUNNEL_TOKEN=${CLOUDFLARE_TUNNEL_TOKEN}
    networks:
      - kapsulenet
    depends_on:
      - nginx
    profiles: ["tunnel"]


  postgres:
    image: postgres:13
    container_name: kapsule-postgres
    # restart: unless-stopped
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - ../data/postgres:/var/lib/postgresql/data
    ports:
      - "${POSTGRES_PORT-5432}:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    networks:
      - kapsulenet

  postgres-init:
    image: postgres:13
    container_name: kapsule-postgres-init
    environment:
      PGHOST: postgres
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - ./postgres-init.sh:/postgres-init.sh:ro
    entrypoint: ["/bin/sh", "/postgres-init.sh"]
    networks:
      - kapsulenet
    depends_on:
      postgres:
        condition: service_healthy
    restart: "no"

networks:
  kapsulenet:
    name: kapsulenet 