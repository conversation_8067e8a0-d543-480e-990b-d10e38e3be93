#!/bin/sh
set -eu

DBS="synapse"
HOST="postgres"
USER="${POSTGRES_USER}"
export PGPASSWORD="${POSTGRES_PASSWORD}"

# Wait until postgres is ready
until pg_isready -h "$HOST" -U "$USER" >/dev/null 2>&1; do
  echo "Waiting for postgres $HOST…"
  sleep 2
done

for db in $DBS; do
  echo "Ensuring database $db exists…"
  exists=$(psql -h "$HOST" -U "$USER" -d postgres -tAc "SELECT 1 FROM pg_database WHERE datname='$db';") || true
  if [ "$exists" != "1" ]; then
    echo "Creating database $db"
    psql -h "$HOST" -U "$USER" -d postgres -c "CREATE DATABASE $db;"
    psql -h "$HOST" -U "$USER" -d postgres -c "GRANT ALL PRIVILEGES ON DATABASE $db TO $USER;"
  fi
done

echo "Database check complete."